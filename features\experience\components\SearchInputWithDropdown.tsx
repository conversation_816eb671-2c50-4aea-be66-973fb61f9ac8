import React, { useState, useRef, useMemo } from "react";
import {
  View,
  TextInput,
  Pressable,
  Text,
  StyleProp,
  ViewStyle,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import globalStyles from "@/lib/globalStyles";
import { useDebounce } from "@/hooks/useDebounce";
import Dropdown, { DropdownItem } from "@/components/Dropdown";
import {
  ExperienceFilterStore,
  ExperienceFilterType,
  searchFieldOptions,
} from "../useExperienceFilterStore";

interface SearchInputWithDropdownProps {
  searchField: ExperienceFilterType;
  onSearchFieldChange: (field: ExperienceFilterType) => void;
  onSearch: (field: ExperienceFilterType) => void;
  placeholder?: string;
  style?: StyleProp<ViewStyle>;
  getFilterResultList: ExperienceFilterStore["getFilterResultList"];
}

const SearchInputWithDropdown: React.FC<SearchInputWithDropdownProps> = ({
  searchField,
  onSearchFieldChange,
  onSearch,
  placeholder,
  style,
  getFilterResultList,
}) => {
  const { t } = useTranslation();
  const [isFieldDropdownVisible, setIsFieldDropdownVisible] = useState(false);
  const [isResultsDropdownVisible, setIsResultsDropdownVisible] =
    useState(false);
  const fieldDropdownRef = useRef<View>(null);
  const resultsDropdownRef = useRef<View>(null);
  const textInputRef = useRef<TextInput>(null);

  const currentFieldLabel = t(searchField.labelKey);

  const getPlaceholder = () => {
    if (placeholder) return placeholder;
    return t("common.search") + " " + currentFieldLabel.toLowerCase();
  };

  const debouncedHandleFieldChange = useDebounce((text: string) => {
    onSearch({ ...searchField, value: text });
  }, 1000);

  const handleFieldSelection = (item: DropdownItem<ExperienceFilterType>) => {
    const field = item.value;
    onSearchFieldChange(field);
    setIsFieldDropdownVisible(false);

    setIsResultsDropdownVisible(false);
    setSearchResults([]);

    textInputRef.current?.clear();

    // Focus text input
    setTimeout(() => {
      textInputRef.current?.focus();
    }, 100);
  };

  // Handle result selection
  const handleResultSelection = (item: DropdownItem<string>) => {
    onSearch({ ...searchField, value: item.value });
    setIsResultsDropdownVisible(false);
    textInputRef.current?.blur();
  };

  const fieldDropdownItems: DropdownItem<ExperienceFilterType>[] =
    searchFieldOptions.map((option) => ({
      id: option.key,
      label: t(option.labelKey),
      value: option,
    }));

  const resultsDropdownItems: DropdownItem<string>[] | undefined = useMemo(
    () =>
      getFilterResultList(searchField.key)?.map((result, index) => ({
        id: index.toString(),
        label: result.value,
        value: result.key,
      })),
    [searchField.key]
  );

  const handleTextInputFocus = () => {
    if (searchField.value && searchResults.length > 0) {
      setIsResultsDropdownVisible(true);
    }
  };

  return (
    <View style={[{ flex: 1 }, style]}>
      <View
        className={cn(
          "flex-row items-center pr-5 pl-3 py-0.5 gap-2.5  bg-light-primary rounded-full overflow-hidden"
        )}
      >
        <Pressable
          ref={fieldDropdownRef}
          onPress={() => setIsFieldDropdownVisible(true)}
          className={cn(
            "flex-row items-center px-3 gap-1 py-1 bg-primary-1 rounded-full"
          )}
        >
          <Text className="text-white text-[12px] font-medium">
            {currentFieldLabel}
          </Text>
          <Ionicons name="caret-down" className="text-white text-base" />
        </Pressable>

        {/* Text Input */}
        <View ref={resultsDropdownRef} className="flex-1">
          <TextInput
            ref={textInputRef}
            defaultValue={searchField.value}
            onChangeText={debouncedHandleFieldChange}
            onFocus={handleTextInputFocus}
            placeholder={getPlaceholder()}
            placeholderTextColor={globalStyles.colors.tertiary2}
            className={cn(" text-base text-primary-1 bg-light-primary")}
          />
        </View>

        {/* Optional Calendar Icon for Date Fields */}
        {searchField.key === "province" && (
          <Pressable
            className="px-2"
            onPress={() => {
              // Could open a province selector modal here
              setIsResultsDropdownVisible(!isResultsDropdownVisible);
            }}
          >
            <Ionicons
              name="location-outline"
              className="text-primary-1 text-xl"
            />
          </Pressable>
        )}
      </View>

      <Dropdown
        items={fieldDropdownItems}
        onSelectItem={handleFieldSelection}
        visible={isFieldDropdownVisible}
        onClose={() => setIsFieldDropdownVisible(false)}
        triggerRef={fieldDropdownRef}
        alignment="left"
      />

      {resultsDropdownItems && (
        <Dropdown
          items={resultsDropdownItems}
          onSelectItem={handleResultSelection}
          visible={isResultsDropdownVisible}
          onClose={() => setIsResultsDropdownVisible(false)}
          triggerRef={resultsDropdownRef}
          alignment="left"
          containerClassName="max-h-[40vh]"
          overlayClassName="bg-black/"
        />
      )}
    </View>
  );
};

export default SearchInputWithDropdown;
