import React, { useState, useRef, useMemo, useEffect } from "react";
import {
  View,
  TextInput,
  Pressable,
  Text,
  StyleProp,
  ViewStyle,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import globalStyles from "@/lib/globalStyles";
import { useDebounce } from "@/hooks/useDebounce";
import Dropdown, { DropdownItem } from "@/components/Dropdown";
import {
  ExperienceFilterStore,
  ExperienceFilterType,
  searchFieldOptions,
} from "../useExperienceFilterStore";

interface SearchInputWithDropdownProps {
  searchField: ExperienceFilterType;
  onSearchFieldChange: (field: ExperienceFilterType) => void;
  onSearch: (field: ExperienceFilterType) => void;
  placeholder?: string;
  style?: StyleProp<ViewStyle>;
  getFilterResultList: ExperienceFilterStore["getFilterResultList"];
}

const SearchInputWithDropdown: React.FC<SearchInputWithDropdownProps> = ({
  searchField,
  onSearchFieldChange,
  onSearch,
  placeholder,
  style,
  getFilterResultList,
}) => {
  const { t } = useTranslation();
  const [isFieldDropdownVisible, setIsFieldDropdownVisible] = useState(false);
  const [isResultsDropdownVisible, setIsResultsDropdownVisible] =
    useState(false);
  const [currentInputText, setCurrentInputText] = useState(
    searchField.value || ""
  );
  const [isTyping, setIsTyping] = useState(false);
  const fieldDropdownRef = useRef<View>(null);
  const resultsDropdownRef = useRef<View>(null);
  const textInputRef = useRef<TextInput>(null);

  const currentFieldLabel = t(searchField.labelKey);

  // Sync currentInputText with searchField.value when field changes
  useEffect(() => {
    setCurrentInputText(searchField.value || "");
  }, [searchField.key, searchField.value]);

  const getPlaceholder = () => {
    if (placeholder) return placeholder;
    return t("common.search") + " " + currentFieldLabel.toLowerCase();
  };

  const debouncedHandleFieldChange = useDebounce((text: string) => {
    setCurrentInputText(text);

    // For select fields, show dropdown with filtered results when typing
    if (searchField.type === "select" && text.trim()) {
      setIsResultsDropdownVisible(true);
    } else if (searchField.type === "select" && !text.trim()) {
      setIsResultsDropdownVisible(false);
    }

    // For text fields, perform the search
    if (searchField.type === "text") {
      onSearch({ ...searchField, value: text });
    }
  }, 300);

  const handleFieldSelection = (item: DropdownItem<ExperienceFilterType>) => {
    const field = item.value;
    onSearchFieldChange(field);
    setIsFieldDropdownVisible(false);

    setIsResultsDropdownVisible(false);
    setCurrentInputText("");
    textInputRef.current?.clear();

    // Focus text input
    setTimeout(() => {
      textInputRef.current?.focus();
    }, 100);
  };

  // Handle result selection
  const handleResultSelection = (item: DropdownItem<string>) => {
    const selectedValue =
      searchField.type === "select" ? item.label : item.value;
    setCurrentInputText(selectedValue);
    onSearch({ ...searchField, value: item.value });
    setIsResultsDropdownVisible(false);
    textInputRef.current?.blur();
  };

  const fieldDropdownItems: DropdownItem<ExperienceFilterType>[] =
    searchFieldOptions.map((option) => ({
      id: option.key,
      label: t(option.labelKey),
      value: option,
    }));

  const resultsDropdownItems: DropdownItem<string>[] | undefined =
    useMemo(() => {
      const allResults = getFilterResultList(searchField.key)?.map(
        (result, index) => ({
          id: index.toString(),
          label: result.value,
          value: result.key,
        })
      );

      // For select fields, filter results based on current input text
      if (
        searchField.type === "select" &&
        currentInputText.trim() &&
        allResults
      ) {
        return allResults.filter((result) =>
          result.label.toLowerCase().includes(currentInputText.toLowerCase())
        );
      }

      return allResults;
    }, [
      searchField.key,
      searchField.type,
      currentInputText,
      getFilterResultList,
    ]);

  const handleTextInputFocus = () => {
    // For select fields, show dropdown if there are results available
    if (
      searchField.type === "select" &&
      resultsDropdownItems &&
      resultsDropdownItems.length > 0
    ) {
      setIsResultsDropdownVisible(true);
    }
    // For text fields, show dropdown if there's a value
    else if (searchField.type === "text" && searchField.value) {
      setIsResultsDropdownVisible(true);
    }
  };

  // Custom close handler for results dropdown
  const handleResultsDropdownClose = () => {
    // For select fields, don't close if user is actively typing or input is focused
    if (
      searchField.type === "select" &&
      (isTyping || textInputRef.current?.isFocused())
    ) {
      // Re-focus the input after a short delay to maintain focus
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 50);
      return;
    }
    setIsResultsDropdownVisible(false);
  };

  return (
    <View style={[{ flex: 1 }, style]}>
      <View
        className={cn(
          "flex-row items-center pr-5 pl-3 py-0.5 gap-2.5  bg-light-primary rounded-full overflow-hidden"
        )}
      >
        <Pressable
          ref={fieldDropdownRef}
          onPress={() => setIsFieldDropdownVisible(true)}
          className={cn(
            "flex-row items-center px-3 gap-1 py-1 bg-primary-1 rounded-full"
          )}
        >
          <Text className="text-white text-[12px] font-medium">
            {currentFieldLabel}
          </Text>
          <Ionicons name="caret-down" className="text-white text-base" />
        </Pressable>

        {/* Text Input */}
        <Pressable
          ref={resultsDropdownRef}
          className="flex-1"
          onPress={() => {
            // Ensure input stays focused when tapping the input area
            textInputRef.current?.focus();
          }}
        >
          <TextInput
            ref={textInputRef}
            value={currentInputText}
            onChangeText={(text) => {
              setCurrentInputText(text);
              setIsTyping(true);
              debouncedHandleFieldChange(text);
              // Reset typing state after a delay
              setTimeout(() => setIsTyping(false), 500);
            }}
            onFocus={handleTextInputFocus}
            onBlur={() => {
              // For select fields, delay closing to allow dropdown interaction
              if (searchField.type === "select") {
                setTimeout(() => {
                  if (!textInputRef.current?.isFocused()) {
                    setIsResultsDropdownVisible(false);
                  }
                }, 150);
              }
            }}
            placeholder={getPlaceholder()}
            placeholderTextColor={globalStyles.colors.tertiary2}
            className={cn(" text-base text-primary-1 bg-light-primary")}
          />
        </Pressable>

        {/* Optional Calendar Icon for Date Fields */}
        {searchField.key === "province" && (
          <Pressable
            className="px-2"
            onPress={() => {
              // Could open a province selector modal here
              setIsResultsDropdownVisible(!isResultsDropdownVisible);
            }}
          >
            <Ionicons
              name="location-outline"
              className="text-primary-1 text-xl"
            />
          </Pressable>
        )}
      </View>

      <Dropdown
        items={fieldDropdownItems}
        onSelectItem={handleFieldSelection}
        visible={isFieldDropdownVisible}
        onClose={() => setIsFieldDropdownVisible(false)}
        triggerRef={fieldDropdownRef}
        alignment="left"
      />

      {resultsDropdownItems && (
        <Dropdown
          items={resultsDropdownItems}
          onSelectItem={handleResultSelection}
          visible={isResultsDropdownVisible}
          onClose={handleResultsDropdownClose}
          triggerRef={resultsDropdownRef}
          alignment="left"
          containerClassName="max-h-[40vh]"
          overlayClassName={
            searchField.type === "select"
              ? "bg-transparent pointer-events-none"
              : "bg-black/"
          }
        />
      )}
    </View>
  );
};

export default SearchInputWithDropdown;
