import React from "react";
import { View } from "react-native";
import { useTranslation } from "react-i18next";
import globalStyles from "@/lib/globalStyles";
import useExperienceFilterStore from "@/features/experience/useExperienceFilterStore";
import SearchInputWithDropdown from "@/features/experience/components/SearchInputWithDropdown";
import FilterControls from "@/features/experience/components/FilterControls";
import ActiveFiltersDisplay from "@/features/experience/components/ActiveFiltersDisplay";

type HeaderExperiencesFilterProps = {
  // Keep for backward compatibility, but now using store
  onSearch?: (text: string) => void;
  onFilterPress?: () => void;
  searchValue?: string;
  searchPlaceholder?: string;
};

const HeaderExperiencesFilter = ({
  searchPlaceholder,
}: HeaderExperiencesFilterProps) => {
  const { t } = useTranslation();
  const {
    sort,
    filterFields,
    searchField,
    setSearchField,
    setSort,
    clearAllFilters,
    addOrUpdateFilter,
    clearFilter,
    getFilterResultList,
  } = useExperienceFilterStore();

  const handleSortChange = (newSortBy?: typeof sort) => {
    setSort(newSortBy);
    newSortBy &&
      addOrUpdateFilter({
        key: "sort",
        type: "select",
        value: newSortBy.key,
        labelKey: "common.sort_by",
        valueKey: `common.${newSortBy.key}`,
      });
  };

  return (
    <View style={{ gap: globalStyles.gap["2xs"] }}>
      {/* Search Input and Filter Controls */}
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: globalStyles.gap["2xs"],
        }}
      >
        <SearchInputWithDropdown
          searchField={searchField}
          onSearch={addOrUpdateFilter}
          onSearchFieldChange={setSearchField}
          placeholder={searchPlaceholder}
          style={{ flex: 1 }}
          getFilterResultList={getFilterResultList}
        />

        <FilterControls
          hasFilters={filterFields.length > 0}
          currentSort={sort}
          onClearFilters={clearAllFilters}
          onSortChange={handleSortChange}
        />
      </View>

      {/* Active Filters Display */}
      <ActiveFiltersDisplay
        filters={filterFields}
        searchField={searchField}
        onRemoveFilter={clearFilter}
      />
    </View>
  );
};

export default HeaderExperiencesFilter;
